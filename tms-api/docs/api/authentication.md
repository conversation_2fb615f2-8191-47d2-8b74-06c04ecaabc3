# Authentication Guide

## Overview

The TMS REST API uses HTTP Basic Authentication for securing endpoints. All endpoints except the health check require authentication.

## Basic Authentication

### Setup

1. **Obtain Credentials**: Get your username and password from the system administrator
2. **Encode Credentials**: Base64 encode your `username:password`
3. **Include in Requests**: Add the `Authorization` header to all requests

### Example

```bash
# Step 1: Encode credentials (replace with your actual credentials)
echo -n "your-username:your-password" | base64
# Output: eW91ci11c2VybmFtZTp5b3VyLXBhc3N3b3Jk

# Step 2: Use in request
curl -H "Authorization: Basic eW91ci11c2VybmFtZTp5b3VyLXBhc3N3b3Jk" \
     -H "X-Correlation-ID: $(uuidgen)" \
     https://api.example.com/quiz/f2f/paperless-marking-worked-solutions
```

## Correlation ID

### Purpose
The `X-Correlation-ID` header is required for request tracing and debugging.

### Format
- Must be a valid UUID (v4 recommended)
- Example: `123e4567-e89b-12d3-a456-************`

### Generation

**JavaScript:**
```javascript
function generateCorrelationId() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}
```

**Python:**
```python
import uuid

correlation_id = str(uuid.uuid4())
```

**cURL:**
```bash
# macOS/Linux
curl -H "X-Correlation-ID: $(uuidgen)" ...

# Or use a fixed UUID for testing
curl -H "X-Correlation-ID: 123e4567-e89b-12d3-a456-************" ...
```

## Error Responses

### 401 Unauthorized
```json
{
  "statusCode": 401,
  "message": "Unauthorized",
  "correlationId": "123e4567-e89b-12d3-a456-************"
}
```

**Causes:**
- Missing `Authorization` header
- Invalid credentials
- Malformed Basic Auth token

### 400 Bad Request (Missing Correlation ID)
```json
{
  "statusCode": 400,
  "message": "X-Correlation-ID header is required",
  "correlationId": null
}
```

## Security Best Practices

1. **Use HTTPS**: Always use HTTPS in production
2. **Secure Storage**: Store credentials securely (environment variables, key vaults)
3. **Rotate Credentials**: Regularly rotate authentication credentials
4. **Monitor Access**: Track API usage with correlation IDs
5. **Validate Responses**: Always check response status codes

## Integration Examples

### JavaScript (Node.js)
```javascript
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

const client = axios.create({
  baseURL: 'https://api.example.com',
  headers: {
    'Authorization': 'Basic ' + Buffer.from('username:password').toString('base64'),
    'X-Correlation-ID': uuidv4(),
  },
});

// Use the client
const response = await client.get('/quiz/f2f/paperless-marking-worked-solutions');
```

### Python
```python
import requests
import uuid
import base64

# Setup authentication
credentials = base64.b64encode(b'username:password').decode('ascii')
headers = {
    'Authorization': f'Basic {credentials}',
    'X-Correlation-ID': str(uuid.uuid4()),
}

# Make request
response = requests.get(
    'https://api.example.com/quiz/f2f/paperless-marking-worked-solutions',
    headers=headers
)
```

### cURL
```bash
#!/bin/bash

# Set credentials (replace with your actual credentials)
USERNAME="your-username"
PASSWORD="your-password"
BASE_URL="https://api.example.com"

# Encode credentials
AUTH_HEADER="Authorization: Basic $(echo -n "$USERNAME:$PASSWORD" | base64)"
CORRELATION_ID="X-Correlation-ID: $(uuidgen)"

# Make request
curl -H "$AUTH_HEADER" \
     -H "$CORRELATION_ID" \
     "$BASE_URL/quiz/f2f/paperless-marking-worked-solutions"
```
