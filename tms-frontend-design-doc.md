# Teaching Material System (TMS) Angular Frontend - Design Document

**Version:** 1.0
**Date:** January 2025
**Author:** Augment Agent
**Stakeholder:** <PERSON> Du

---

## 1. Introduction

### 1.1 Purpose
This document outlines the technical design and architecture for the Angular frontend application that interfaces with the existing Teaching Material System (TMS) REST API. The design prioritizes simplicity, maintainability, and seamless integration with the established backend infrastructure.

### 1.2 Design Philosophy
- **User-Centric**: Primary focus on quiz ZIP file upload workflow
- **Simplicity Over Complexity**: Minimal, clean architecture avoiding over-engineering
- **API-First Integration**: Designed around existing TMS REST API patterns
- **Progressive Enhancement**: Core functionality first, advanced features later
- **Docker-Ready**: Full containerization support from day one

### 1.3 Scope
The frontend application will provide a complete web interface for:
- Quiz ZIP file upload with drag-and-drop functionality
- Quiz management and retrieval
- Authentication and session management
- Error handling and user feedback
- System monitoring and health checks

---

## 2. Architecture Overview

### 2.1 High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Angular Frontend                         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Upload    │  │    Quiz     │  │   System    │         │
│  │  Component  │  │ Management  │  │ Dashboard   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │    Auth     │  │     API     │  │   Error     │         │
│  │   Service   │  │   Service   │  │  Handling   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                HTTP Interceptors                            │
│         (Auth, Correlation ID, Error Handling)              │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ HTTPS/REST API
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    TMS REST API                             │
│              (Existing Backend System)                      │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 Technology Stack

#### Core Framework
- **Angular**: v17+ (latest stable)
- **TypeScript**: v5+
- **RxJS**: v7+ for reactive programming
- **Angular CLI**: Latest for build and development tools

#### UI/UX Framework
- **Angular Material**: Primary UI component library
- **Angular CDK**: For advanced component behaviors
- **Angular Flex Layout**: Responsive layout system
- **Custom CSS**: For brand-specific styling

#### Development Tools
- **ESLint**: Code quality and consistency
- **Prettier**: Code formatting
- **Jasmine/Karma**: Unit testing
- **Cypress**: End-to-end testing
- **Angular DevTools**: Development debugging

#### Build and Deployment
- **Angular CLI**: Build optimization and bundling
- **Docker**: Containerization with nginx
- **Environment Configuration**: Development/production configs

---

## 3. Component Architecture

### 3.1 Application Structure

```
src/
├── app/
│   ├── core/                    # Singleton services and guards
│   │   ├── services/
│   │   │   ├── api.service.ts
│   │   │   ├── auth.service.ts
│   │   │   └── correlation.service.ts
│   │   ├── guards/
│   │   │   └── auth.guard.ts
│   │   ├── interceptors/
│   │   │   ├── auth.interceptor.ts
│   │   │   ├── correlation.interceptor.ts
│   │   │   └── error.interceptor.ts
│   │   └── core.module.ts
│   ├── shared/                  # Reusable components and utilities
│   │   ├── components/
│   │   │   ├── loading/
│   │   │   ├── error-display/
│   │   │   └── confirmation-dialog/
│   │   ├── pipes/
│   │   ├── directives/
│   │   └── shared.module.ts
│   ├── features/                # Feature modules
│   │   ├── upload/
│   │   │   ├── components/
│   │   │   ├── services/
│   │   │   └── upload.module.ts
│   │   ├── quiz-management/
│   │   │   ├── components/
│   │   │   ├── services/
│   │   │   └── quiz-management.module.ts
│   │   └── dashboard/
│   │       ├── components/
│   │       ├── services/
│   │       └── dashboard.module.ts
│   ├── layout/                  # Application layout components
│   │   ├── header/
│   │   ├── sidebar/
│   │   ├── footer/
│   │   └── layout.module.ts
│   ├── app-routing.module.ts
│   ├── app.component.ts
│   └── app.module.ts
├── assets/                      # Static assets
├── environments/                # Environment configurations
└── styles/                      # Global styles
```

### 3.2 Core Components

#### 3.2.1 Upload Component (`features/upload`)
**Purpose**: Primary quiz ZIP file upload interface

**Key Features**:
- Drag-and-drop file upload area
- File validation (type, size, structure)
- Metadata input form (year, term, week, weekType, teachingProgram)
- Upload progress tracking with cancellation
- Success/error result display

**Component Structure**:
```typescript
@Component({
  selector: 'app-upload',
  templateUrl: './upload.component.html',
  styleUrls: ['./upload.component.scss']
})
export class UploadComponent {
  uploadForm: FormGroup;
  uploadProgress$ = new BehaviorSubject<number>(0);
  uploadResult$ = new BehaviorSubject<any>(null);

  onFileSelected(file: File): void;
  onDragOver(event: DragEvent): void;
  onDrop(event: DragEvent): void;
  uploadFile(): void;
  cancelUpload(): void;
}
```

#### 3.2.2 Quiz Management Component (`features/quiz-management`)
**Purpose**: Browse, search, and manage existing quiz materials

**Key Features**:
- Advanced filtering (grade, subject, course, classLevel, color, year, term, week)
- Sortable data table with pagination
- Quiz detail view with metadata and asset links
- Update and delete operations
- Bulk operations support

**Component Structure**:
```typescript
@Component({
  selector: 'app-quiz-management',
  templateUrl: './quiz-management.component.html',
  styleUrls: ['./quiz-management.component.scss']
})
export class QuizManagementComponent {
  quizzes$ = new BehaviorSubject<Quiz[]>([]);
  filters = new FormGroup({...});
  selectedQuiz$ = new BehaviorSubject<Quiz | null>(null);

  loadQuizzes(): void;
  applyFilters(): void;
  selectQuiz(quiz: Quiz): void;
  updateQuiz(quiz: Quiz): void;
  deleteQuiz(id: number): void;
}
```

#### 3.2.3 Dashboard Component (`features/dashboard`)
**Purpose**: System overview and monitoring

**Key Features**:
- System health status
- Recent upload activity
- Upload statistics and metrics
- Error log summary
- Quick access to common actions

---

## 4. Service Architecture

### 4.1 Core Services

#### 4.1.1 API Service (`core/services/api.service.ts`)
**Purpose**: Central HTTP client for TMS REST API integration

**Key Methods**:
```typescript
@Injectable({ providedIn: 'root' })
export class ApiService {
  private baseUrl = environment.apiUrl;

  // Quiz operations
  uploadQuiz(file: File, metadata: QuizUploadMetadata): Observable<QuizUploadResponse>;
  getQuizzes(filters: QuizFilters): Observable<Quiz[]>;
  getQuiz(id: number): Observable<Quiz>;
  updateQuiz(id: number, updates: Partial<Quiz>): Observable<Quiz>;
  deleteQuiz(id: number): Observable<void>;

  // System operations
  getHealth(): Observable<HealthStatus>;
}
```

#### 4.1.2 Authentication Service (`core/services/auth.service.ts`)
**Purpose**: Handle user authentication and session management

**Key Methods**:
```typescript
@Injectable({ providedIn: 'root' })
export class AuthService {
  private isAuthenticated$ = new BehaviorSubject<boolean>(false);

  login(username: string, password: string): Observable<boolean>;
  logout(): void;
  isLoggedIn(): Observable<boolean>;
  getAuthHeader(): string;
}
```

#### 4.1.3 Correlation Service (`core/services/correlation.service.ts`)
**Purpose**: Generate and manage correlation IDs for request tracing

**Key Methods**:
```typescript
@Injectable({ providedIn: 'root' })
export class CorrelationService {
  generateCorrelationId(): string;
  getCurrentCorrelationId(): string;
  setCorrelationId(id: string): void;
}
```

### 4.2 HTTP Interceptors

#### 4.2.1 Authentication Interceptor
**Purpose**: Automatically add authentication headers to API requests

#### 4.2.2 Correlation Interceptor
**Purpose**: Add correlation IDs to all outgoing requests

#### 4.2.3 Error Interceptor
**Purpose**: Centralized error handling and user notification

---

## 5. User Interface Design

### 5.1 Design System

#### Color Palette
- **Primary**: Material Blue (#1976D2)
- **Accent**: Material Orange (#FF9800)
- **Success**: Material Green (#4CAF50)
- **Warning**: Material Amber (#FFC107)
- **Error**: Material Red (#F44336)

#### Typography
- **Headers**: Roboto, 24px/20px/16px
- **Body**: Roboto, 14px
- **Captions**: Roboto, 12px

#### Spacing
- **Base Unit**: 8px
- **Component Spacing**: 16px, 24px, 32px
- **Section Spacing**: 48px, 64px

### 5.2 Key Interface Patterns

#### 5.2.1 Upload Interface
- **Large Drop Zone**: Prominent, centered upload area
- **Visual Feedback**: Drag-over highlighting and animations
- **Progress Indication**: Linear progress bar with percentage
- **Metadata Form**: Clean, organized form layout
- **Result Display**: Success/error cards with detailed information

#### 5.2.2 Data Tables
- **Material Design**: Consistent with Angular Material patterns
- **Sorting**: Click-to-sort column headers
- **Filtering**: Expandable filter panel
- **Pagination**: Standard Material pagination component
- **Actions**: Row-level and bulk action buttons

#### 5.2.3 Navigation
- **Top Navigation**: Application header with user menu
- **Side Navigation**: Collapsible sidebar for main sections
- **Breadcrumbs**: Context-aware navigation path
- **Quick Actions**: Floating action button for primary tasks

---

## 6. Data Models and Interfaces

### 6.1 Core Interfaces

```typescript
// Quiz data model
interface Quiz {
  id: number;
  retrievedMetadata: {
    subject: string;
    grade: number;
    classLevel: string;
    lessonName: string;
    color: string;
    year: number;
    term: number;
    week: number;
    weekType: 'normal' | 'holiday';
    course: string;
    teachingProgram: string;
    internalMetadata: any[];
    originalFilename: string;
    uploadTimestamp: string;
  };
  gifUrls: Array<{
    id: string;
    url: string;
  }>;
}

// Upload metadata
interface QuizUploadMetadata {
  year: number;
  term: number;
  week: number;
  weekType: 'normal' | 'holiday';
  teachingProgram?: string;
}

// Upload response
interface QuizUploadResponse {
  message: string;
  data: {
    quizId: number;
    metadataPath: string;
    uploadedGifs: Array<{
      questionId: string;
      objectName: string;
    }>;
    gifCount: number;
    extractedMetadata: {
      grade: number;
      subject: string;
      course: string;
      classLevel: string;
      color: string;
      topic: string;
    };
    uploadTimestamp: string;
    originalFilename: string;
  };
}

// Filter criteria
interface QuizFilters {
  grade?: number;
  subject?: string;
  course?: string;
  classLevel?: string;
  color?: string;
  year?: number;
  term?: number;
  week?: number;
  weekType?: 'normal' | 'holiday';
  teachingProgram?: string;
  lessonName?: string;
}
```

---

## 7. Error Handling Strategy

### 7.1 Error Categories

#### Client-Side Errors
- **Validation Errors**: Form validation and file validation
- **Network Errors**: Connection failures and timeouts
- **Authentication Errors**: Invalid credentials or expired sessions

#### Server-Side Errors
- **API Errors**: Backend validation and processing errors
- **System Errors**: Database or file storage failures
- **Rate Limiting**: Throttling and quota exceeded errors

### 7.2 Error Handling Patterns

#### Global Error Interceptor
```typescript
@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(req).pipe(
      catchError((error: HttpErrorResponse) => {
        // Log error with correlation ID
        // Show user-friendly notification
        // Handle specific error types
        return throwError(error);
      })
    );
  }
}
```

#### Component-Level Error Handling
- **Form Validation**: Real-time validation with clear error messages
- **Upload Errors**: Detailed error display with retry options
- **Network Failures**: Automatic retry with exponential backoff

---

## 8. Performance Optimization

### 8.1 Loading Strategies

#### Lazy Loading
- **Feature Modules**: Load modules on-demand
- **Route-Based**: Split bundles by route
- **Component-Level**: Lazy load heavy components

#### Caching Strategies
- **HTTP Caching**: Leverage browser caching for static assets
- **Service Caching**: Cache API responses where appropriate
- **State Management**: Efficient state updates and subscriptions

### 8.2 Bundle Optimization

#### Build Configuration
- **Tree Shaking**: Remove unused code
- **Code Splitting**: Separate vendor and application bundles
- **Compression**: Gzip compression for production builds
- **Source Maps**: Development-only source maps

---

## 9. Testing Strategy

### 9.1 Unit Testing
- **Component Testing**: Test component logic and templates
- **Service Testing**: Test API integration and business logic
- **Pipe Testing**: Test custom pipes and transformations
- **Coverage Target**: 80%+ code coverage

### 9.2 Integration Testing
- **API Integration**: Test service-to-API communication
- **Component Integration**: Test component interactions
- **Route Testing**: Test navigation and guards

### 9.3 End-to-End Testing
- **User Workflows**: Test complete user journeys
- **File Upload**: Test upload functionality with real files
- **Error Scenarios**: Test error handling and recovery

---

## 10. Deployment Architecture

### 10.1 Docker Configuration

#### Multi-Stage Build
```dockerfile
# Build stage
FROM node:18-alpine AS build
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build --prod

# Production stage
FROM nginx:alpine
COPY --from=build /app/dist/tms-frontend /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### Environment Configuration
- **Development**: Local development with hot reload
- **Production**: Optimized build with nginx serving
- **Environment Variables**: API URLs and configuration

### 10.2 Integration with Existing Infrastructure
- **Docker Compose**: Integration with existing TMS services
- **Networking**: Same Docker network as TMS API
- **SSL/HTTPS**: Nginx configuration for SSL termination
- **Monitoring**: Health checks and logging integration

---

## 11. Security Considerations

### 11.1 Authentication Security
- **Credential Storage**: Secure storage of authentication tokens
- **Session Management**: Automatic logout and session timeout
- **HTTPS Only**: All communications over HTTPS

### 11.2 Input Validation
- **Client-Side Validation**: Immediate feedback and UX
- **File Validation**: Type, size, and content validation
- **XSS Prevention**: Sanitization of user inputs

### 11.3 API Security
- **CORS Configuration**: Proper cross-origin resource sharing
- **Request Validation**: Validate all outgoing requests
- **Error Information**: No sensitive data in error messages

---

## 12. Monitoring and Observability

### 12.1 Application Monitoring
- **Error Tracking**: Client-side error logging
- **Performance Metrics**: Page load times and user interactions
- **User Analytics**: Usage patterns and feature adoption

### 12.2 Integration Monitoring
- **API Health**: Monitor backend API availability
- **Upload Success Rates**: Track upload success/failure rates
- **Response Times**: Monitor API response performance

---

## 13. Future Enhancements

### 13.1 Phase 2 Features
- **Progressive Web App**: Offline capability and mobile optimization
- **Advanced Analytics**: Detailed usage analytics and reporting
- **Batch Operations**: Multiple file upload and bulk management
- **Real-time Updates**: WebSocket integration for live updates

### 13.2 Technical Improvements
- **State Management**: NgRx for complex state management
- **Micro-frontends**: Modular architecture for scalability
- **Advanced Caching**: Service worker and advanced caching strategies
- **Internationalization**: Multi-language support

---

## 14. Development Guidelines

### 14.1 Code Standards
- **TypeScript**: Strict mode with comprehensive typing
- **ESLint**: Consistent code quality and style
- **Component Architecture**: Single responsibility principle
- **Service Design**: Dependency injection and testability

### 14.2 Git Workflow
- **Feature Branches**: Isolated development for each feature
- **Code Reviews**: Mandatory peer review process
- **Commit Messages**: Conventional commit format
- **Continuous Integration**: Automated testing and building

---

## 15. API Integration Patterns

### 15.1 HTTP Client Configuration

#### Base HTTP Service
```typescript
@Injectable({ providedIn: 'root' })
export class HttpClientService {
  private readonly baseUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  get<T>(endpoint: string, options?: any): Observable<T> {
    return this.http.get<T>(`${this.baseUrl}${endpoint}`, options);
  }

  post<T>(endpoint: string, body: any, options?: any): Observable<T> {
    return this.http.post<T>(`${this.baseUrl}${endpoint}`, body, options);
  }

  put<T>(endpoint: string, body: any, options?: any): Observable<T> {
    return this.http.put<T>(`${this.baseUrl}${endpoint}`, body, options);
  }

  delete<T>(endpoint: string, options?: any): Observable<T> {
    return this.http.delete<T>(`${this.baseUrl}${endpoint}`, options);
  }
}
```

#### File Upload Service
```typescript
@Injectable({ providedIn: 'root' })
export class FileUploadService {
  constructor(private httpClient: HttpClientService) {}

  uploadQuizFile(file: File, metadata: QuizUploadMetadata): Observable<QuizUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('year', metadata.year.toString());
    formData.append('term', metadata.term.toString());
    formData.append('week', metadata.week.toString());
    formData.append('weekType', metadata.weekType);

    if (metadata.teachingProgram) {
      formData.append('teachingProgram', metadata.teachingProgram);
    }

    return this.httpClient.post<QuizUploadResponse>(
      '/quiz/f2f/paperless-marking-worked-solutions',
      formData,
      {
        reportProgress: true,
        observe: 'events'
      }
    ).pipe(
      map(event => this.handleUploadProgress(event)),
      filter(response => response instanceof HttpResponse),
      map(response => response.body)
    );
  }

  private handleUploadProgress(event: HttpEvent<any>): any {
    switch (event.type) {
      case HttpEventType.UploadProgress:
        const progress = Math.round(100 * event.loaded / event.total!);
        return { type: 'progress', progress };
      case HttpEventType.Response:
        return event;
      default:
        return event;
    }
  }
}
```

### 15.2 Error Response Handling

#### Error Response Interface
```typescript
interface ApiErrorResponse {
  statusCode: number;
  message: string;
  details?: string | object;
  correlationId?: string;
  timestamp: string;
  path: string;
}
```

#### Error Handling Service
```typescript
@Injectable({ providedIn: 'root' })
export class ErrorHandlingService {
  handleApiError(error: HttpErrorResponse): string {
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      return `Network error: ${error.error.message}`;
    } else {
      // Server-side error
      const apiError = error.error as ApiErrorResponse;
      return this.formatServerError(apiError);
    }
  }

  private formatServerError(apiError: ApiErrorResponse): string {
    switch (apiError.statusCode) {
      case 400:
        return `Validation error: ${apiError.message}`;
      case 401:
        return 'Authentication required. Please log in.';
      case 404:
        return 'Resource not found.';
      case 413:
        return 'File too large. Maximum size is 50MB.';
      case 500:
        return 'Server error. Please try again later.';
      default:
        return apiError.message || 'An unexpected error occurred.';
    }
  }
}
```

---

## 16. State Management Strategy

### 16.1 Component State Management

#### Upload Component State
```typescript
interface UploadState {
  selectedFile: File | null;
  uploadProgress: number;
  uploadStatus: 'idle' | 'uploading' | 'success' | 'error';
  uploadResult: QuizUploadResponse | null;
  error: string | null;
  metadata: QuizUploadMetadata;
}

@Component({...})
export class UploadComponent {
  private state$ = new BehaviorSubject<UploadState>({
    selectedFile: null,
    uploadProgress: 0,
    uploadStatus: 'idle',
    uploadResult: null,
    error: null,
    metadata: {
      year: new Date().getFullYear(),
      term: 1,
      week: 1,
      weekType: 'normal'
    }
  });

  // Selectors
  selectedFile$ = this.state$.pipe(map(state => state.selectedFile));
  uploadProgress$ = this.state$.pipe(map(state => state.uploadProgress));
  uploadStatus$ = this.state$.pipe(map(state => state.uploadStatus));
  isUploading$ = this.state$.pipe(map(state => state.uploadStatus === 'uploading'));

  // Actions
  selectFile(file: File): void {
    this.updateState({ selectedFile: file, error: null });
  }

  updateMetadata(metadata: Partial<QuizUploadMetadata>): void {
    this.updateState({
      metadata: { ...this.state$.value.metadata, ...metadata }
    });
  }

  private updateState(partial: Partial<UploadState>): void {
    this.state$.next({ ...this.state$.value, ...partial });
  }
}
```

### 16.2 Service-Level State Management

#### Quiz Management State Service
```typescript
@Injectable({ providedIn: 'root' })
export class QuizStateService {
  private quizzes$ = new BehaviorSubject<Quiz[]>([]);
  private loading$ = new BehaviorSubject<boolean>(false);
  private filters$ = new BehaviorSubject<QuizFilters>({});

  // Public observables
  readonly quizzes = this.quizzes$.asObservable();
  readonly loading = this.loading$.asObservable();
  readonly filters = this.filters$.asObservable();

  constructor(private apiService: ApiService) {}

  loadQuizzes(filters?: QuizFilters): void {
    this.loading$.next(true);
    this.apiService.getQuizzes(filters || {}).subscribe({
      next: (quizzes) => {
        this.quizzes$.next(quizzes);
        this.loading$.next(false);
      },
      error: (error) => {
        this.loading$.next(false);
        // Handle error
      }
    });
  }

  updateFilters(filters: QuizFilters): void {
    this.filters$.next(filters);
    this.loadQuizzes(filters);
  }

  addQuiz(quiz: Quiz): void {
    const currentQuizzes = this.quizzes$.value;
    this.quizzes$.next([...currentQuizzes, quiz]);
  }

  removeQuiz(id: number): void {
    const currentQuizzes = this.quizzes$.value;
    this.quizzes$.next(currentQuizzes.filter(quiz => quiz.id !== id));
  }
}
```

---

## 17. Responsive Design Implementation

### 17.1 Breakpoint Strategy

#### CSS Custom Properties
```scss
:root {
  // Breakpoints
  --breakpoint-xs: 0;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1400px;

  // Container widths
  --container-sm: 540px;
  --container-md: 720px;
  --container-lg: 960px;
  --container-xl: 1140px;
  --container-xxl: 1320px;
}
```

#### Angular Flex Layout Integration
```typescript
@Component({
  template: `
    <div fxLayout="row" fxLayout.xs="column" fxLayoutGap="16px">
      <div fxFlex="30" fxFlex.xs="100">
        <!-- Sidebar content -->
      </div>
      <div fxFlex="70" fxFlex.xs="100">
        <!-- Main content -->
      </div>
    </div>
  `
})
export class ResponsiveLayoutComponent {}
```

### 17.2 Mobile-First Upload Interface

#### Mobile Upload Component
```scss
.upload-area {
  min-height: 200px;
  border: 2px dashed #ccc;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;

  @media (max-width: 768px) {
    min-height: 150px;
    margin: 16px;
  }

  &.drag-over {
    border-color: #1976d2;
    background-color: rgba(25, 118, 210, 0.1);
  }

  &.has-file {
    border-color: #4caf50;
    background-color: rgba(76, 175, 80, 0.1);
  }
}

.upload-content {
  text-align: center;
  padding: 24px;

  @media (max-width: 768px) {
    padding: 16px;
  }

  .upload-icon {
    font-size: 48px;
    color: #666;
    margin-bottom: 16px;

    @media (max-width: 768px) {
      font-size: 36px;
      margin-bottom: 12px;
    }
  }

  .upload-text {
    font-size: 16px;
    color: #666;

    @media (max-width: 768px) {
      font-size: 14px;
    }
  }
}
```

---

## 18. Accessibility Implementation

### 18.1 WCAG 2.1 AA Compliance

#### Keyboard Navigation
```typescript
@Component({
  template: `
    <div class="upload-area"
         tabindex="0"
         role="button"
         [attr.aria-label]="uploadAreaLabel"
         (keydown.enter)="openFileDialog()"
         (keydown.space)="openFileDialog()">
      <!-- Upload content -->
    </div>
  `
})
export class AccessibleUploadComponent {
  uploadAreaLabel = 'Click or press Enter to select a file for upload';

  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.openFileDialog();
    }
  }
}
```

#### Screen Reader Support
```typescript
@Component({
  template: `
    <div [attr.aria-live]="uploadStatus === 'uploading' ? 'polite' : 'off'">
      <span class="sr-only" *ngIf="uploadStatus === 'uploading'">
        Upload in progress: {{uploadProgress}}% complete
      </span>
      <span class="sr-only" *ngIf="uploadStatus === 'success'">
        Upload completed successfully
      </span>
      <span class="sr-only" *ngIf="uploadStatus === 'error'">
        Upload failed: {{errorMessage}}
      </span>
    </div>
  `
})
export class AccessibleProgressComponent {}
```

#### Color and Contrast
```scss
// High contrast color scheme
.high-contrast {
  --primary-color: #000000;
  --secondary-color: #ffffff;
  --success-color: #008000;
  --error-color: #ff0000;
  --warning-color: #ffff00;

  // Ensure minimum 4.5:1 contrast ratio
  .text-primary { color: var(--primary-color); }
  .bg-primary { background-color: var(--primary-color); }
  .text-success { color: var(--success-color); }
  .text-error { color: var(--error-color); }
}
```

---

## 19. Performance Monitoring

### 19.1 Core Web Vitals Tracking

#### Performance Service
```typescript
@Injectable({ providedIn: 'root' })
export class PerformanceService {
  constructor() {
    this.initializePerformanceObserver();
  }

  private initializePerformanceObserver(): void {
    if ('PerformanceObserver' in window) {
      // Largest Contentful Paint
      new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          console.log('LCP:', entry.startTime);
          // Send to analytics
        }
      }).observe({ entryTypes: ['largest-contentful-paint'] });

      // First Input Delay
      new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          console.log('FID:', entry.processingStart - entry.startTime);
          // Send to analytics
        }
      }).observe({ entryTypes: ['first-input'] });

      // Cumulative Layout Shift
      new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!entry.hadRecentInput) {
            console.log('CLS:', entry.value);
            // Send to analytics
          }
        }
      }).observe({ entryTypes: ['layout-shift'] });
    }
  }

  measureUploadPerformance(startTime: number, fileSize: number): void {
    const endTime = performance.now();
    const duration = endTime - startTime;
    const throughput = fileSize / (duration / 1000); // bytes per second

    console.log('Upload Performance:', {
      duration: `${duration.toFixed(2)}ms`,
      throughput: `${(throughput / 1024 / 1024).toFixed(2)} MB/s`,
      fileSize: `${(fileSize / 1024 / 1024).toFixed(2)} MB`
    });
  }
}
```

### 19.2 Bundle Analysis and Optimization

#### Webpack Bundle Analyzer Integration
```json
{
  "scripts": {
    "analyze": "ng build --prod --stats-json && npx webpack-bundle-analyzer dist/tms-frontend/stats.json"
  }
}
```

#### Lazy Loading Performance
```typescript
// Route-based code splitting
const routes: Routes = [
  {
    path: 'upload',
    loadChildren: () => import('./features/upload/upload.module').then(m => m.UploadModule)
  },
  {
    path: 'quiz-management',
    loadChildren: () => import('./features/quiz-management/quiz-management.module').then(m => m.QuizManagementModule)
  },
  {
    path: 'dashboard',
    loadChildren: () => import('./features/dashboard/dashboard.module').then(m => m.DashboardModule)
  }
];
```

---

This comprehensive design document provides a complete blueprint for developing a robust, maintainable Angular frontend that seamlessly integrates with the existing TMS REST API while following established development principles and best practices. The design emphasizes simplicity, performance, accessibility, and maintainability while providing a superior user experience for quiz file management.
